/**
 * RecordingSessionProvider - Isolated Auto-Advance Logic
 * Handles recording session state, phrase progression, and auto-advance functionality
 * This provider isolates the complex auto-advance logic to prevent race conditions
 */

import React, { createContext, useContext, useReducer, useEffect, useCallback } from 'react';
import { phraseCollectionConfig } from '../phrases';
import {
  getSelectedPhrases,
  saveSelectedPhrases
} from '../services/phraseRotationService';

// Action types
const RECORDING_ACTIONS = {
  SET_SELECTED_PHRASES: 'SET_SELECTED_PHRASES',
  SET_CURRENT_PHRASE_INDEX: 'SET_CURRENT_PHRASE_INDEX',
  SET_CURRENT_RECORDING_NUMBER: 'SET_CURRENT_RECORDING_NUMBER',
  SET_SELECTED_CATEGORY: 'SET_SELECTED_CATEGORY',
  UPDATE_RECORDINGS_COUNT: 'UPDATE_RECORDINGS_COUNT',
  SET_COMPLETION_PROMPT: 'SET_COMPLETION_PROMPT',
  RESET_RECORDING_SESSION: 'RESET_RECORDING_SESSION',
  SET_PHRASES_SELECTED: 'SET_PHRASES_SELECTED'
};

// Initial state
const initialState = {
  selectedPhrases: null,
  currentPhraseIndex: 0,
  currentRecordingNumber: 1,
  selectedCategory: '',
  recordingsCount: {},
  showCompletionPrompt: false,
  phrasesSelected: false,
  
  // Configuration
  RECORDINGS_PER_PHRASE: phraseCollectionConfig.recordingsPerPhrase,
  COLLECTION_GOAL: phraseCollectionConfig.collectionGoal
};

// Reducer with focused state management
const recordingSessionReducer = (state, action) => {
  switch (action.type) {
    case RECORDING_ACTIONS.SET_SELECTED_PHRASES:
      return {
        ...state,
        selectedPhrases: action.payload,
        currentPhraseIndex: 0,
        selectedCategory: action.payload?.[0]?.category || ''
      };
      
    case RECORDING_ACTIONS.SET_CURRENT_PHRASE_INDEX:
      return {
        ...state,
        currentPhraseIndex: action.payload
      };
      
    case RECORDING_ACTIONS.SET_CURRENT_RECORDING_NUMBER:
      return {
        ...state,
        currentRecordingNumber: action.payload
      };
      
    case RECORDING_ACTIONS.SET_SELECTED_CATEGORY:
      return {
        ...state,
        selectedCategory: action.payload
      };
      
    case RECORDING_ACTIONS.UPDATE_RECORDINGS_COUNT:
      const newRecordingsCount = {
        ...state.recordingsCount,
        [action.payload.phraseKey]: action.payload.count
      };
      
      // Persist to localStorage
      try {
        localStorage.setItem('icuAppRecordingsCount', JSON.stringify(newRecordingsCount));
      } catch (error) {
        console.warn('⚠️ Failed to save recording counts to localStorage:', error);
      }
      
      return {
        ...state,
        recordingsCount: newRecordingsCount
      };
      
    case RECORDING_ACTIONS.SET_COMPLETION_PROMPT:
      return {
        ...state,
        showCompletionPrompt: action.payload
      };
      
    case RECORDING_ACTIONS.SET_PHRASES_SELECTED:
      return {
        ...state,
        phrasesSelected: action.payload
      };
      
    case RECORDING_ACTIONS.RESET_RECORDING_SESSION:
      return {
        ...initialState,
        recordingsCount: state.recordingsCount // Preserve recording counts
      };
      
    default:
      return state;
  }
};

// Context
const RecordingSessionContext = createContext();

// Provider component
export const RecordingSessionProvider = ({ children }) => {
  const [state, dispatch] = useReducer(recordingSessionReducer, initialState);

  // Load recording counts from localStorage on mount
  useEffect(() => {
    try {
      const savedRecordingsCount = localStorage.getItem('icuAppRecordingsCount');
      if (savedRecordingsCount) {
        const parsedCounts = JSON.parse(savedRecordingsCount);
        
        // Update state with loaded counts
        Object.entries(parsedCounts).forEach(([phraseKey, count]) => {
          dispatch({
            type: RECORDING_ACTIONS.UPDATE_RECORDINGS_COUNT,
            payload: { phraseKey, count }
          });
        });
        
        console.log('📱 Recording counts loaded from localStorage:', parsedCounts);
      }
    } catch (error) {
      console.warn('⚠️ Failed to load recording counts from localStorage:', error);
    }

    // Load selected phrases if they exist
    const loadedPhrases = getSelectedPhrases();
    if (loadedPhrases && loadedPhrases.length > 0) {
      dispatch({
        type: RECORDING_ACTIONS.SET_SELECTED_PHRASES,
        payload: loadedPhrases
      });
      dispatch({
        type: RECORDING_ACTIONS.SET_PHRASES_SELECTED,
        payload: true
      });
    }
  }, []);

  // ISOLATED AUTO-ADVANCE LOGIC - This is the key fix for auto-advance issues
  useEffect(() => {
    if (!state.selectedPhrases || state.selectedPhrases.length === 0 || state.currentPhraseIndex < 0) {
      return;
    }

    const currentPhraseObj = state.selectedPhrases[state.currentPhraseIndex];
    if (!currentPhraseObj) return;

    const phraseKey = `${currentPhraseObj.category}:${currentPhraseObj.phrase}`;
    const currentCount = state.recordingsCount[phraseKey] || 0;

    console.log('🔄 AUTO-ADVANCE CHECK:', {
      phrase: currentPhraseObj.phrase,
      currentCount,
      required: state.RECORDINGS_PER_PHRASE,
      shouldAdvance: currentCount >= state.RECORDINGS_PER_PHRASE
    });

    if (currentCount >= state.RECORDINGS_PER_PHRASE) {
      console.log('🎯 AUTO-ADVANCE: Phrase completion detected');

      // Check if this is the last phrase
      if (state.currentPhraseIndex >= state.selectedPhrases.length - 1) {
        console.log('🏁 AUTO-ADVANCE: All phrases completed, checking if all phrases are done...');

        // Verify all phrases are completed before showing completion prompt
        const incompletePhrase = state.selectedPhrases.find(phrase => {
          const phraseKey = `${phrase.category}:${phrase.phrase}`;
          const count = state.recordingsCount[phraseKey] || 0;
          return count < state.RECORDINGS_PER_PHRASE;
        });

        if (!incompletePhrase) {
          console.log('🏁 AUTO-ADVANCE: All phrases completed, showing completion prompt');
          dispatch({
            type: RECORDING_ACTIONS.SET_COMPLETION_PROMPT,
            payload: true
          });
        } else {
          console.log('⚠️ AUTO-ADVANCE: Not all phrases completed yet:', incompletePhrase);
        }
      } else {
        console.log('📝 AUTO-ADVANCE: Moving to next phrase');
        const nextPhraseIndex = state.currentPhraseIndex + 1;
        const nextPhrase = state.selectedPhrases[nextPhraseIndex];

        // Update phrase index
        dispatch({
          type: RECORDING_ACTIONS.SET_CURRENT_PHRASE_INDEX,
          payload: nextPhraseIndex
        });

        // Update category if needed
        if (nextPhrase.category !== state.selectedCategory) {
          dispatch({
            type: RECORDING_ACTIONS.SET_SELECTED_CATEGORY,
            payload: nextPhrase.category
          });
        }

        // Set recording number for next phrase
        const nextPhraseKey = `${nextPhrase.category}:${nextPhrase.phrase}`;
        const existingRecordings = state.recordingsCount[nextPhraseKey] || 0;
        dispatch({
          type: RECORDING_ACTIONS.SET_CURRENT_RECORDING_NUMBER,
          payload: existingRecordings + 1
        });
      }
    }
  }, [state.recordingsCount, state.currentPhraseIndex, state.selectedPhrases, state.RECORDINGS_PER_PHRASE]);

  // Action creators
  const actions = {
    setSelectedPhrases: useCallback((phrases) => {
      dispatch({
        type: RECORDING_ACTIONS.SET_SELECTED_PHRASES,
        payload: phrases
      });
      dispatch({
        type: RECORDING_ACTIONS.SET_PHRASES_SELECTED,
        payload: true
      });
      saveSelectedPhrases(phrases);
    }, []),
    
    recordingCompleted: useCallback((metadata) => {
      const phraseKey = `${metadata.category}:${metadata.phrase}`;
      const currentCount = state.recordingsCount[phraseKey] || 0;
      const newCount = currentCount + 1;
      
      console.log('📹 RECORDING COMPLETED:', {
        phrase: metadata.phrase,
        previousCount: currentCount,
        newCount,
        phraseKey
      });
      
      // Update recording count - this will trigger auto-advance useEffect
      dispatch({
        type: RECORDING_ACTIONS.UPDATE_RECORDINGS_COUNT,
        payload: { phraseKey, count: newCount }
      });
      
      // Update current recording number
      dispatch({
        type: RECORDING_ACTIONS.SET_CURRENT_RECORDING_NUMBER,
        payload: newCount + 1
      });
    }, [state.recordingsCount]),
    
    setCurrentPhraseIndex: useCallback((index) => {
      dispatch({
        type: RECORDING_ACTIONS.SET_CURRENT_PHRASE_INDEX,
        payload: index
      });
    }, []),
    
    setCompletionPrompt: useCallback((show) => {
      dispatch({
        type: RECORDING_ACTIONS.SET_COMPLETION_PROMPT,
        payload: show
      });
    }, []),
    
    resetSession: useCallback(() => {
      dispatch({ type: RECORDING_ACTIONS.RESET_RECORDING_SESSION });
    }, [])
  };

  // Computed values
  const currentPhrase = state.selectedPhrases && state.selectedPhrases[state.currentPhraseIndex]
    ? state.selectedPhrases[state.currentPhraseIndex].phrase
    : '';

  const value = {
    ...state,
    ...actions,
    currentPhrase
  };

  return (
    <RecordingSessionContext.Provider value={value}>
      {children}
    </RecordingSessionContext.Provider>
  );
};

// Hook to use recording session
export const useRecordingSession = () => {
  const context = useContext(RecordingSessionContext);
  if (!context) {
    throw new Error('useRecordingSession must be used within a RecordingSessionProvider');
  }
  return context;
};

export default RecordingSessionProvider;
