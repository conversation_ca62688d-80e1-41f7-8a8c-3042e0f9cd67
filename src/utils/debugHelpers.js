/**
 * Debug Helpers - Development Only
 * All debugging functionality extracted from App.js to reduce production complexity
 * Only loaded in development mode to keep production bundle clean
 */

// Only enable debug helpers in development mode
const isDevelopment = process.env.NODE_ENV === 'development';

class DebugHelpers {
  constructor() {
    this.isEnabled = isDevelopment;
    this.recordingSessionContext = null;
    this.appStateContext = null;
    this.sessionStateContext = null;
  }

  // Initialize debug helpers with context references
  initialize() {
    if (!this.isEnabled) return;

    // Debug helpers will access contexts through window object
    // This avoids hook call issues during initialization
    this.attachGlobalDebugFunctions();
    console.log('🔧 Debug helpers initialized for development mode');
  }

  // Attach debug functions to window object for browser console access
  attachGlobalDebugFunctions() {
    if (!this.isEnabled) return;

    // Test progress bar with sample data
    window.testProgressBar = () => {
      console.log('🧪 Testing progress bar with sample data');
      const testData = {
        'Basic Needs:I need water': 2,
        'Basic Needs:I need food': 3,
        'Health:I am in pain': 1,
        'Communication:Call my family': 3
      };

      console.log('🧪 Test data would be applied:', testData);
      console.log('🧪 Note: Direct context manipulation disabled to prevent hook violations');
    };

    // Test completion page display
    window.testCompletionPage = () => {
      console.log('🧪 Testing completion page display');
      console.log('🧪 Note: Direct context manipulation disabled to prevent hook violations');
      console.log('🧪 Use browser React DevTools to test completion page');
    };

    // Force recording count to 3 for testing auto-advance
    window.debugForceRecordingCount = () => {
      console.log('🔧 DEBUG: Force recording count function');
      console.log('🔧 Note: Direct context manipulation disabled to prevent hook violations');
      console.log('🔧 Use browser React DevTools to test auto-advance functionality');
    };

    // Test auto-advancement manually
    window.debugTestAutoAdvancement = () => {
      console.log('🧪 === TESTING AUTO-ADVANCEMENT ===');
      console.log('🧪 Note: Direct context manipulation disabled to prevent hook violations');
      console.log('🧪 Use browser React DevTools to test auto-advance functionality');
    };

    // Check current application state
    window.debugCurrentState = () => {
      console.log('🔍 === CURRENT APPLICATION STATE ===');
      console.log('🔍 Note: Direct context access disabled to prevent hook violations');
      console.log('🔍 Use browser React DevTools to inspect component state');
      console.log('🔍 Check localStorage for recording counts:');
      console.log('  - icuAppRecordingsCount:', JSON.parse(localStorage.getItem('icuAppRecordingsCount') || '{}'));
      console.log('  - icuAppDemographics:', JSON.parse(localStorage.getItem('icuAppDemographics') || 'null'));
    };

    // Check training video state
    window.debugTrainingVideoState = () => {
      console.log('🎬 === TRAINING VIDEO DEBUG STATE ===');
      console.log('🎬 Note: Direct context access disabled to prevent hook violations');
      console.log('🎬 Use browser React DevTools to inspect training video state');
    };

    // Trigger early exit completion
    window.triggerEarlyExitCompletion = () => {
      console.log('🚪 Early exit completion triggered via debug function');

      // Mark as early exit in localStorage
      try {
        localStorage.setItem('icuCompletionData', JSON.stringify({
          earlyExit: true,
          timestamp: new Date().toISOString()
        }));
        console.log('✅ Early exit marked in localStorage');
        console.log('🚪 Note: Use browser React DevTools to trigger completion prompt');
      } catch (error) {
        console.warn('⚠️ Failed to mark early exit:', error);
      }
    };

    console.log('🔧 Global debug functions attached to window object:');
    console.log('  - window.testProgressBar()');
    console.log('  - window.testCompletionPage()');
    console.log('  - window.debugForceRecordingCount()');
    console.log('  - window.debugTestAutoAdvancement()');
    console.log('  - window.debugCurrentState()');
    console.log('  - window.debugTrainingVideoState()');
    console.log('  - window.triggerEarlyExitCompletion()');
  }

  // Log comprehensive state for debugging
  logAppState(context, additionalData = {}) {
    if (!this.isEnabled) return;

    console.log(`📊 === APP STATE DEBUG (${context}) ===`);
    console.log('📊 Note: Direct context access disabled to prevent hook violations');
    console.log('📊 Use browser React DevTools to inspect component state');

    if (additionalData && Object.keys(additionalData).length > 0) {
      console.log('Additional Data:', additionalData);
    }
  }

  // Performance monitoring
  startPerformanceMonitoring() {
    if (!this.isEnabled) return;

    console.log('📈 Starting performance monitoring...');
    
    // Monitor React re-renders
    if (window.React) {
      // Add React DevTools profiler if available
      console.log('React DevTools available for performance profiling');
    }
    
    // Monitor memory usage
    if (performance.memory) {
      const logMemory = () => {
        console.log('💾 Memory usage:', {
          used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024) + ' MB',
          total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024) + ' MB',
          limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024) + ' MB'
        });
      };
      
      // Log memory every 30 seconds
      setInterval(logMemory, 30000);
      logMemory(); // Initial log
    }
  }

  // Clean up debug helpers
  cleanup() {
    if (!this.isEnabled) return;

    // Remove global debug functions
    const debugFunctions = [
      'testProgressBar',
      'testCompletionPage', 
      'debugForceRecordingCount',
      'debugTestAutoAdvancement',
      'debugCurrentState',
      'debugTrainingVideoState',
      'triggerEarlyExitCompletion'
    ];
    
    debugFunctions.forEach(funcName => {
      if (window[funcName]) {
        delete window[funcName];
      }
    });
    
    console.log('🧹 Debug helpers cleaned up');
  }
}

// Create singleton instance
const debugHelpers = new DebugHelpers();

export default debugHelpers;
