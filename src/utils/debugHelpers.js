/**
 * Debug Helpers - Development Only
 * All debugging functionality extracted from App.js to reduce production complexity
 * Only loaded in development mode to keep production bundle clean
 */

// Only enable debug helpers in development mode
const isDevelopment = process.env.NODE_ENV === 'development';

class DebugHelpers {
  constructor() {
    this.isEnabled = isDevelopment;
    this.recordingSessionContext = null;
    this.appStateContext = null;
    this.sessionStateContext = null;
  }

  // Initialize debug helpers with context references
  initialize(contexts) {
    if (!this.isEnabled) return;

    this.recordingSessionContext = contexts.recordingSession;
    this.appStateContext = contexts.appState;
    this.sessionStateContext = contexts.sessionState;

    this.attachGlobalDebugFunctions();
    console.log('🔧 Debug helpers initialized for development mode');
  }

  // Attach debug functions to window object for browser console access
  attachGlobalDebugFunctions() {
    if (!this.isEnabled) return;

    // Test progress bar with sample data
    window.testProgressBar = () => {
      console.log('🧪 Testing progress bar with sample data');
      const testData = {
        'Basic Needs:I need water': 2,
        'Basic Needs:I need food': 3,
        'Health:I am in pain': 1,
        'Communication:Call my family': 3
      };
      
      if (this.recordingSessionContext) {
        // Update recording counts for testing
        Object.entries(testData).forEach(([phraseKey, count]) => {
          this.recordingSessionContext.recordingCompleted({
            category: phraseKey.split(':')[0],
            phrase: phraseKey.split(':')[1]
          });
        });
      }
      console.log('🧪 Test data applied:', testData);
    };

    // Test completion page display
    window.testCompletionPage = () => {
      console.log('🧪 Testing completion page display');
      
      if (this.recordingSessionContext) {
        this.recordingSessionContext.setCompletionPrompt(true);
      }
      
      if (this.sessionStateContext) {
        this.sessionStateContext.incrementSessionRecordings();
        this.sessionStateContext.generateSessionReference({
          testMode: true
        });
      }
      
      console.log('🧪 Completion page should now be visible');
    };

    // Force recording count to 3 for testing auto-advance
    window.debugForceRecordingCount = () => {
      console.log('🔧 DEBUG: Forcing recording count to 3 for current phrase');
      
      if (!this.recordingSessionContext?.selectedPhrases || 
          !this.recordingSessionContext?.currentPhraseIndex >= 0) {
        console.log('❌ No phrases selected or invalid phrase index');
        return;
      }

      const currentPhrase = this.recordingSessionContext.selectedPhrases[
        this.recordingSessionContext.currentPhraseIndex
      ];
      
      if (currentPhrase) {
        // Simulate 3 recordings for current phrase
        for (let i = 0; i < 3; i++) {
          this.recordingSessionContext.recordingCompleted({
            category: currentPhrase.category,
            phrase: currentPhrase.phrase
          });
        }
        console.log('✅ Forced 3 recordings for:', currentPhrase.phrase);
        console.log('⏳ Auto-advancement should trigger automatically...');
      }
    };

    // Test auto-advancement manually
    window.debugTestAutoAdvancement = () => {
      console.log('🧪 === TESTING AUTO-ADVANCEMENT ===');
      
      if (!this.recordingSessionContext?.selectedPhrases?.length) {
        console.log('❌ No phrases selected');
        return;
      }

      const currentPhrase = this.recordingSessionContext.selectedPhrases[
        this.recordingSessionContext.currentPhraseIndex
      ];
      
      if (!currentPhrase) {
        console.log('❌ No current phrase');
        return;
      }

      console.log('Current phrase:', currentPhrase.phrase);
      console.log('Current recordings:', this.recordingSessionContext.recordingsCount[
        `${currentPhrase.category}:${currentPhrase.phrase}`
      ] || 0);
      
      // Force completion
      this.recordingSessionContext.recordingCompleted({
        category: currentPhrase.category,
        phrase: currentPhrase.phrase
      });
      
      console.log('✅ Recording completion triggered - auto-advance should activate');
    };

    // Check current application state
    window.debugCurrentState = () => {
      console.log('🔍 === CURRENT APPLICATION STATE ===');
      
      if (this.recordingSessionContext) {
        console.log('Recording Session:', {
          selectedPhrases: this.recordingSessionContext.selectedPhrases?.length || 0,
          currentPhraseIndex: this.recordingSessionContext.currentPhraseIndex,
          currentRecordingNumber: this.recordingSessionContext.currentRecordingNumber,
          recordingsCount: this.recordingSessionContext.recordingsCount,
          showCompletionPrompt: this.recordingSessionContext.showCompletionPrompt
        });
      }
      
      if (this.appStateContext) {
        console.log('App State:', {
          hasConsent: this.appStateContext.hasConsent,
          demographicsCompleted: this.appStateContext.demographicsCompleted,
          currentStep: this.appStateContext.currentStep,
          uploading: this.appStateContext.uploading
        });
      }
      
      if (this.sessionStateContext) {
        console.log('Session State:', {
          demographicInfo: !!this.sessionStateContext.demographicInfo,
          sessionRecordingsCount: this.sessionStateContext.sessionRecordingsCount,
          currentSessionReference: this.sessionStateContext.currentSessionReference
        });
      }
    };

    // Check training video state
    window.debugTrainingVideoState = () => {
      console.log('🎬 === TRAINING VIDEO DEBUG STATE ===');
      
      if (this.appStateContext) {
        console.log('Training flow state:', {
          hasConsent: this.appStateContext.hasConsent,
          demographicsCompleted: this.appStateContext.demographicsCompleted,
          trainingVideoCompleted: this.appStateContext.trainingVideoCompleted,
          currentStep: this.appStateContext.currentStep
        });
        
        const shouldShowTraining = this.appStateContext.hasConsent && 
                                  this.appStateContext.demographicsCompleted && 
                                  this.appStateContext.currentStep === 'training';
        console.log('Should show training video?', shouldShowTraining);
      }
    };

    // Trigger early exit completion
    window.triggerEarlyExitCompletion = () => {
      console.log('🚪 Early exit completion triggered via debug function');
      
      if (this.recordingSessionContext) {
        this.recordingSessionContext.setCompletionPrompt(true);
      }
      
      // Mark as early exit in localStorage
      try {
        localStorage.setItem('icuCompletionData', JSON.stringify({
          earlyExit: true,
          timestamp: new Date().toISOString()
        }));
        console.log('✅ Early exit marked in localStorage');
      } catch (error) {
        console.warn('⚠️ Failed to mark early exit:', error);
      }
    };

    console.log('🔧 Global debug functions attached to window object:');
    console.log('  - window.testProgressBar()');
    console.log('  - window.testCompletionPage()');
    console.log('  - window.debugForceRecordingCount()');
    console.log('  - window.debugTestAutoAdvancement()');
    console.log('  - window.debugCurrentState()');
    console.log('  - window.debugTrainingVideoState()');
    console.log('  - window.triggerEarlyExitCompletion()');
  }

  // Log comprehensive state for debugging
  logAppState(context, additionalData = {}) {
    if (!this.isEnabled) return;

    console.log(`📊 === APP STATE DEBUG (${context}) ===`);
    
    if (this.recordingSessionContext) {
      console.log('Recording Session:', {
        currentPhraseIndex: this.recordingSessionContext.currentPhraseIndex,
        currentRecordingNumber: this.recordingSessionContext.currentRecordingNumber,
        recordingsCount: this.recordingSessionContext.recordingsCount,
        selectedPhrasesLength: this.recordingSessionContext.selectedPhrases?.length,
        showCompletionPrompt: this.recordingSessionContext.showCompletionPrompt,
        currentPhrase: this.recordingSessionContext.currentPhrase
      });
    }
    
    if (additionalData && Object.keys(additionalData).length > 0) {
      console.log('Additional Data:', additionalData);
    }
  }

  // Performance monitoring
  startPerformanceMonitoring() {
    if (!this.isEnabled) return;

    console.log('📈 Starting performance monitoring...');
    
    // Monitor React re-renders
    if (window.React) {
      // Add React DevTools profiler if available
      console.log('React DevTools available for performance profiling');
    }
    
    // Monitor memory usage
    if (performance.memory) {
      const logMemory = () => {
        console.log('💾 Memory usage:', {
          used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024) + ' MB',
          total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024) + ' MB',
          limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024) + ' MB'
        });
      };
      
      // Log memory every 30 seconds
      setInterval(logMemory, 30000);
      logMemory(); // Initial log
    }
  }

  // Clean up debug helpers
  cleanup() {
    if (!this.isEnabled) return;

    // Remove global debug functions
    const debugFunctions = [
      'testProgressBar',
      'testCompletionPage', 
      'debugForceRecordingCount',
      'debugTestAutoAdvancement',
      'debugCurrentState',
      'debugTrainingVideoState',
      'triggerEarlyExitCompletion'
    ];
    
    debugFunctions.forEach(funcName => {
      if (window[funcName]) {
        delete window[funcName];
      }
    });
    
    console.log('🧹 Debug helpers cleaned up');
  }
}

// Create singleton instance
const debugHelpers = new DebugHelpers();

export default debugHelpers;
